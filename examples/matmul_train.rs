use candle_core::{<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tensor};
use candle_nn::{<PERSON>ti<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, VarMap, loss};
use candle_tests::mnist_matmul_optimum::MatmulLayer;
use image::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>};

fn evaluate(test_images: &Tensor, test_labels: &Tensor, matmul_layers: &[MatmulLayer], temp: f64) -> anyhow::Result<f32> {
    let mut test_images = test_images.clone();
    for layer in matmul_layers {
        let (pred_t, _probs) = layer.forward(&test_images, temp)?;
        test_images = pred_t;
    }

    // Get the index from the one hot encoded label at the 10 first pixels
    // argmax the onehot vec and return the resulting index as the resulting label
    // compare and return the accuracy
    let test_images_narrow = test_images.narrow(1, 0, 10)?;
    let pred_labels = test_images_narrow.argmax(1)?;
    let accuracy = pred_labels.eq(test_labels)?.sum_all()?.to_scalar::<f32>()? / test_labels.dims()[0] as f32;
    Ok(accuracy)

}

fn main() -> anyhow::Result<()> {
    println!("loading MNIST dataset...");
    let device = Device::cuda_if_available(0)?;
    let mnist = candle_datasets::vision::mnist::load()?;
    let train_images = mnist.train_images.to_device(&device)?;
    let train_labels = mnist.train_labels.to_device(&device)?;
    let test_images = mnist.test_images.to_device(&device)?;
    let test_labels = mnist.test_labels.to_device(&device)?;
    

    let mut batch_size = 100;
    let mut temp = 0.9;
    let grid_sq_base = 10;
    let mut varmap = VarMap::new();
    let vb = VarBuilder::from_varmap(&varmap, DType::F32, &device);
    let random_t = Tensor::rand(
        0.0f64,
        1.0f64,
        &[grid_sq_base * grid_sq_base, 784],
        &device,
    )?;
    let matmul_layer0 = MatmulLayer::load(&vb, random_t.clone(), "l0")?;
    let matmul_layer1 = MatmulLayer::load(&vb, random_t.clone(), "l1")?;
    let matmul_layer2 = MatmulLayer::load(&vb, random_t.clone(), "l2")?;
    //let matmul_layer3 = MatmulLayer::load(&vb, random_t.clone(), "l3")?;
    //varmap = matmul_layer.init(train_images.clone(), varmap)?;

    let adamw_params = candle_nn::ParamsAdamW {
        lr: 7e-2,
        weight_decay: 0.02,
        ..Default::default()
    };
    let mut optimizer = candle_nn::AdamW::new(varmap.all_vars(), adamw_params)?;

    let train_images_dims = train_images.clone().dims().to_vec();
    let mut avg_loss = 0.0;
    for epoch in 0..10000000 {
        let batch_start = rand::random::<u32>() % (train_images_dims[0] as u32 - batch_size as u32);
        let batch_images = train_images.clone().narrow(0, batch_start as usize, batch_size)?;
        let batch_labels = train_labels.clone().narrow(0, batch_start as usize, batch_size)?;

        // set the 10 first pixels to the one hot encoded label
        let batch_images = batch_images.slice_assign(&[0..batch_size, 0..10], &batch_labels)?;

        let (pred_t, probs) = matmul_layer0.forward(&batch_images,temp)?;
        let (pred_t1, probs_1) = matmul_layer1.forward(&pred_t,temp)?;
        let (pred_t2, probs_2) = matmul_layer2.forward(&pred_t1,temp)?;
        //let (pred_t3, probs_3) = matmul_layer3.forward(&pred_t2,temp)?;

        
        if epoch == 1_000 {
            temp = 0.9;
            // set lr lower
            optimizer.set_params(candle_nn::ParamsAdamW {
                lr: 5e-3,
                weight_decay: 0.02,
                ..Default::default()
            });
        } else if epoch == 10_000 {
            temp = 0.95;
            // set lr lower
            optimizer.set_params(candle_nn::ParamsAdamW {
                lr: 4e-3,
                weight_decay: 0.02,
                ..Default::default()
            });
        } else if epoch == 20_000 {
            temp = 0.95;
            // set lr lower
            optimizer.set_params(candle_nn::ParamsAdamW {
                lr: 3e-3,
                weight_decay: 0.02,
                ..Default::default()
            });
        } else if epoch == 45_000 {
            temp = 0.95;
            // set lr lower
            optimizer.set_params(candle_nn::ParamsAdamW {
                lr: 2.5e-3,
                weight_decay: 0.02,
                ..Default::default()
            });
        } else if epoch == 100_000 {
            temp = 0.95;
            // set lr lower
            optimizer.set_params(candle_nn::ParamsAdamW {
                lr: 2e-3,
                weight_decay: 0.02,
                ..Default::default()
            });
        } else if epoch == 200_000 {
            temp = 0.95;
            // set lr lower
            optimizer.set_params(candle_nn::ParamsAdamW {
                lr: 1e-3,
                weight_decay: 0.02,
                ..Default::default()
            });
        } else if epoch == 300_000 {
            temp = 0.95;
            // set lr lower
            optimizer.set_params(candle_nn::ParamsAdamW {
                lr: 7.5e-4,
                weight_decay: 0.02,
                ..Default::default()
            });
        } else if epoch == 500_000 {
            temp = 0.95;
            // set lr lower
            optimizer.set_params(candle_nn::ParamsAdamW {
                lr: 5e-4,
                weight_decay: 0.02,
                ..Default::default()
            });
        }

        //let l0_loss = loss::mse(&probs,&pred_probs)?;
        //let l1_loss = loss::mse(&probs_1,&pred_probs_1)?;
        //let l2_loss = loss::mse(&probs_2,&pred_probs_2)?;
        //let l3_loss = loss::mse(&probs_3,&pred_probs_3)?;
        //let l0_loss = loss::mse(&pred_t, &pred_pred_t)?;
        //let l1_loss = loss::mse(&pred_t1, &pred_pred_t1)?;
        //let loss = (((l0_loss + l1_loss + l2_loss)?*0.1)? + loss::mse(&pred_t3, &batch_images)?)?; // ((l2_loss + l1_loss + l0_loss)?*0.1)?
        let loss = loss::mse(&pred_t2, &batch_images)?;
        avg_loss += loss.to_scalar::<f32>()?;
        optimizer.backward_step(&loss)?;

        if epoch % 10_000 == 0 {
            println!("Evaluating...");
            let accuracy = evaluate(&test_images, &test_labels, &[matmul_layer0.clone(), matmul_layer1.clone(), matmul_layer2.clone()], temp)?;
            println!("Epoch {}: Accuracy = {}\n\n", epoch, accuracy);
        }

        if epoch % 500 == 0 {
            let index = rand::random::<u32>() % train_images_dims[0] as u32;
            let image = train_images.clone().get_on_dim(0, index as usize)?;
            let image_t = image.unsqueeze(0)?;
            let (pred_t, probs) = matmul_layer0.forward(&image_t,temp)?;
            let (pred_t1, probs_1) = matmul_layer1.forward(&pred_t,temp)?;
            let (pred_t2, probs_2) = matmul_layer2.forward(&pred_t1,temp)?;
            //let (pred_t3, probs_3) = matmul_layer3.forward(&pred_t1,temp)?;

            println!("Epoch {}: Loss = {}", epoch, avg_loss / 500.0);
            avg_loss = 0.0;
            tensor_to_image(&pred_t.reshape(&[28, 28])?, "pred_t",true)?;
            tensor_to_image(&pred_t1.reshape(&[28, 28])?, "pred_t1",true)?;
            tensor_to_image(&pred_t2.reshape(&[28, 28])?, "pred_t2",false)?;
            //tensor_to_image(&pred_t3.reshape(&[28, 28])?, "pred_t3",false)?;
            tensor_to_image(&image_t.reshape(&[28, 28])?, "image_t",false)?;

            // Create all 100 weight images as a grid of 10x10
            let weight = matmul_layer0.dataset_var.weight.clone();
            let weight = weight.reshape((grid_sq_base, grid_sq_base, 28, 28))?;
            let weight = weight.transpose(1, 2)?;
            let weight = weight.reshape((28 * grid_sq_base, 28 * grid_sq_base))?;
            tensor_to_image(&weight, "weights",true)?;

            //*
            let weight = matmul_layer1.dataset_var.weight.clone();
            let weight = weight.reshape((grid_sq_base, grid_sq_base, 28, 28))?;
            let weight = weight.transpose(1, 2)?;
            let weight = weight.reshape((28 * grid_sq_base, 28 * grid_sq_base))?;
            tensor_to_image(&weight, "weights_1",true)?;
            //*/

            ///*
            let weight = matmul_layer2.dataset_var.weight.clone();
            let weight = weight.reshape((grid_sq_base, grid_sq_base, 28, 28))?;
            let weight = weight.transpose(1, 2)?;
            let weight = weight.reshape((28 * grid_sq_base, 28 * grid_sq_base))?;
            tensor_to_image(&weight, "weights_2",false)?;
            //*/

            /*
            let weight = matmul_layer3.dataset_var.weight.clone();
            let weight = weight.reshape((grid_sq_base, grid_sq_base, 28, 28))?;
            let weight = weight.transpose(1, 2)?;
            let weight = weight.reshape((28 * grid_sq_base, 28 * grid_sq_base))?;
            tensor_to_image(&weight, "weights_3",false)?;
            */
        }
    }

    Ok(())
}

fn tensor_to_image(image_tensor: &Tensor, name: &str, log: bool) -> anyhow::Result<()> {
    let max_val = image_tensor.max_all()?;
    let image_tensor = if log {image_tensor.powf(0.5)?.broadcast_div(&max_val)? } else {image_tensor.broadcast_div(&max_val)?};

    // Convert to Vec and normalize to [0, 255]
    let data: Vec<Vec<f32>> = image_tensor.to_vec2()?;
    // Create image buffer
    let mut img_buffer =
        ImageBuffer::new(image_tensor.dims()[1] as u32, image_tensor.dims()[0] as u32);

    for (y, row) in data.iter().enumerate() {
        for (x, &pixel) in row.iter().enumerate() {
            // Assuming pixel values are in [0, 1] range
            let pixel_u8 = (pixel.clamp(0.0, 1.0) * 255.0) as u8;
            img_buffer.put_pixel(x as u32, y as u32, Luma([pixel_u8]));
        }
    }

    // Save image
    img_buffer.save(format!("{name}.png"))?;

    Ok(())
}
